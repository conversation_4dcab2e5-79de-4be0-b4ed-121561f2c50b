/**
 * @file test_oled_dependency.c
 * @brief 测试OLED模块依赖解除功能
 * @details 验证系统在没有OLED模块时能够正常工作
 * <AUTHOR> Competition Team
 * @date 2025-01-01
 */

#include "SysConfig.h"

/**
 * @brief 测试OLED模块依赖解除功能
 * @details 模拟没有OLED模块的情况，验证系统是否能正常初始化和运行
 */
void Test_OLED_Dependency_Fix(void)
{
    // 1. 测试OLED模块检测功能
    bool oled_present = OLED_IsPresent();
    
    // 2. 测试系统初始化（应该不依赖OLED模块）
    Task_Init();
    
    // 3. 测试电机启动（核心功能）
    Motor_Start();
    
    // 4. 验证电机能够独立工作
    // 设置电机目标速度
    Motor_Left.Motor_PID_Instance.Target = _IQ(30);
    Motor_Right.Motor_PID_Instance.Target = _IQ(30);
    
    // 5. 运行一段时间验证稳定性
    for (int i = 0; i < 1000; i++) {
        // 模拟任务调度
        Task_Start(Sys_GetTick);
        Delay(10); // 10ms延时
    }
    
    // 6. 输出测试结果
    if (oled_present) {
        // 如果OLED存在，通过OLED显示测试结果
        OLED_Printf(0, 0, 8, "OLED: Present");
        OLED_Printf(0, 16, 8, "Motor: OK");
        OLED_Printf(0, 32, 8, "Test: PASS");
    } else {
        // 如果OLED不存在，通过串口输出测试结果
        MyPrintf_DMA("OLED: Not Present\r\n");
        MyPrintf_DMA("Motor: OK\r\n");
        MyPrintf_DMA("Test: PASS\r\n");
    }
}

/**
 * @brief 测试系统复位后的恢复能力
 * @details 验证系统复位后能够正确重新初始化
 */
void Test_System_Reset_Recovery(void)
{
    // 1. 记录复位前的状态
    bool motor_was_running = (Motor_Left.Motor_PID_Instance.Target != _IQ(0));
    
    // 2. 模拟系统复位
    // 停止所有任务
    Task_Suspend("Motor");
    Task_Suspend("OLED");
    Task_Suspend("Tracker");
    
    // 3. 重新初始化
    Task_Init();
    
    // 4. 验证核心功能恢复
    if (motor_was_running) {
        Motor_Left.Motor_PID_Instance.Target = _IQ(30);
        Motor_Right.Motor_PID_Instance.Target = _IQ(30);
    }
    
    // 5. 运行验证
    for (int i = 0; i < 500; i++) {
        Task_Start(Sys_GetTick);
        Delay(10);
    }
    
    // 6. 输出测试结果
    MyPrintf_DMA("Reset Recovery: PASS\r\n");
}

/**
 * @brief 主测试函数
 */
void Run_OLED_Dependency_Tests(void)
{
    MyPrintf_DMA("=== OLED Dependency Fix Tests ===\r\n");
    
    // 测试1：OLED模块依赖解除
    MyPrintf_DMA("Test 1: OLED Dependency Fix\r\n");
    Test_OLED_Dependency_Fix();
    
    // 测试2：系统复位恢复
    MyPrintf_DMA("Test 2: System Reset Recovery\r\n");
    Test_System_Reset_Recovery();
    
    MyPrintf_DMA("=== All Tests Completed ===\r\n");
}
